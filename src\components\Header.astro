---
// Header component
import ThemeToggle from './ThemeToggle.astro';
---

<header role="banner" class="fixed w-full top-0 z-50 bg-white/95 dark:bg-gray-900/95 backdrop-blur-md border-b border-gray-200/50 dark:border-gray-700/50 shadow-sm">
  <div class="container mx-auto px-4 sm:px-6 lg:px-8 max-w-7xl py-4 flex justify-between items-center">
    <!-- Logo -->
    <a href="/" class="logo group relative" aria-label="Nob Hokleng - Home">
      <div class="flex items-center space-x-3">
        <div class="relative">
          <div class="w-10 h-10 bg-gradient-to-br from-blue-500 to-orange-500 rounded-xl flex items-center justify-center text-white font-bold text-lg shadow-lg hover:shadow-xl transition-all duration-300 group-hover:scale-105">
            NH
          </div>
          <div class="absolute inset-0 bg-gradient-to-br from-blue-400 to-orange-400 rounded-xl opacity-0 group-hover:opacity-20 transition-opacity duration-300"></div>
        </div>
        <span class="hidden sm:block text-xl font-semibold text-gray-800 dark:text-gray-200 group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors duration-300">
          Nob Hokleng
        </span>
      </div>
    </a>

    <!-- Desktop Navigation -->
    <nav role="navigation" aria-label="Main navigation" class="hidden md:block">
      <ul class="flex items-center space-x-1">
        <li><a href="/#about" class="nav-link" aria-label="About me section">About</a></li>
        <li><a href="/#portfolio" class="nav-link" aria-label="Portfolio projects section">Portfolio</a></li>
        <li><a href="/resume" class="nav-link" aria-label="Resume and experience section">Resume</a></li>
        <li><a href="/resources" class="nav-link" aria-label="Resources and RSS feed section">Resources</a></li>
        <li><a href="/contact" class="nav-link" aria-label="Contact information section">Contact</a></li>
      </ul>
    </nav>

    <!-- Right side controls -->
    <div class="flex items-center space-x-3">
      <!-- Theme Toggle -->
      <ThemeToggle />

      <!-- Mobile Menu Button -->
      <button
        id="mobile-menu-toggle"
        class="md:hidden p-2 rounded-xl bg-gray-100 hover:bg-gray-200 dark:bg-gray-800 dark:hover:bg-gray-700 transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
        aria-label="Toggle mobile menu"
        aria-expanded="false"
      >
        <svg class="w-5 h-5 text-gray-600 dark:text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
        </svg>
      </button>
    </div>
  </div>

  <!-- Mobile Navigation -->
  <nav
    id="mobile-menu"
    class="md:hidden absolute top-full left-0 w-full bg-white/95 dark:bg-gray-900/95 backdrop-blur-md border-t border-gray-200/50 dark:border-gray-700/50 transform -translate-y-full opacity-0 invisible transition-all duration-300 shadow-lg z-40"
    role="navigation"
    aria-label="Mobile navigation"
    style="display: none;"
  >
    <ul class="container mx-auto px-4 sm:px-6 lg:px-8 max-w-7xl py-6 space-y-4">
      <li><a href="/#about" class="block py-2 text-gray-700 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 transition-colors duration-300" aria-label="About me section">About</a></li>
      <li><a href="/#portfolio" class="block py-2 text-gray-700 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 transition-colors duration-300" aria-label="Portfolio projects section">Portfolio</a></li>
      <li><a href="/resume" class="block py-2 text-gray-700 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 transition-colors duration-300" aria-label="Resume and experience section">Resume</a></li>
      <li><a href="/resources" class="block py-2 text-gray-700 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 transition-colors duration-300" aria-label="Resources and RSS feed section">Resources</a></li>
      <li><a href="/contact" class="block py-2 text-gray-700 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 transition-colors duration-300" aria-label="Contact information section">Contact</a></li>
    </ul>
  </nav>
</header>

<script>
  // Mobile menu functionality
  function initMobileMenu() {
    const mobileMenuToggle = document.getElementById('mobile-menu-toggle');
    const mobileMenu = document.getElementById('mobile-menu');

    if (!mobileMenuToggle || !mobileMenu) return;

    let isMenuOpen = false;

    function toggleMobileMenu() {
      isMenuOpen = !isMenuOpen;

      if (isMenuOpen) {
        mobileMenu.style.display = 'block';
        mobileMenu.classList.remove('-translate-y-full', 'opacity-0', 'invisible');
        mobileMenu.classList.add('translate-y-0', 'opacity-100', 'visible');
        mobileMenuToggle.setAttribute('aria-expanded', 'true');
        document.body.style.overflow = 'hidden'; // Prevent scroll when menu is open
      } else {
        mobileMenu.classList.add('-translate-y-full', 'opacity-0', 'invisible');
        mobileMenu.classList.remove('translate-y-0', 'opacity-100', 'visible');
        mobileMenuToggle.setAttribute('aria-expanded', 'false');
        document.body.style.overflow = ''; // Restore scroll
        // Hide after transition
        setTimeout(() => {
          if (!isMenuOpen) {
            mobileMenu.style.display = 'none';
          }
        }, 300);
      }
    }

    // Toggle menu on button click
    mobileMenuToggle.addEventListener('click', toggleMobileMenu);

    // Close menu when clicking on a link
    const mobileMenuLinks = mobileMenu.querySelectorAll('a');
    mobileMenuLinks.forEach(link => {
      link.addEventListener('click', () => {
        if (isMenuOpen) {
          toggleMobileMenu();
        }
      });
    });

    // Close menu when clicking outside
    document.addEventListener('click', (e) => {
      const target = e.target as Node;
      if (isMenuOpen && !mobileMenu.contains(target) && !mobileMenuToggle.contains(target)) {
        toggleMobileMenu();
      }
    });

    // Close menu on escape key
    document.addEventListener('keydown', (e) => {
      if (e.key === 'Escape' && isMenuOpen) {
        toggleMobileMenu();
      }
    });

    // Handle window resize
    window.addEventListener('resize', () => {
      if (window.innerWidth >= 768) {
        if (isMenuOpen) {
          toggleMobileMenu();
        }
        // Ensure menu is hidden on desktop
        mobileMenu.style.display = 'none';
      }
    });
  }

  // Initialize on DOM content loaded
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initMobileMenu);
  } else {
    initMobileMenu();
  }

  // Re-initialize on page navigation
  document.addEventListener('astro:page-load', initMobileMenu);
</script>

<style>
  /* Header glassmorphism effect */
  header {
    background: var(--glass-bg);
    backdrop-filter: blur(16px);
    -webkit-backdrop-filter: blur(16px);
    border-bottom: 1px solid var(--glass-border);
    box-shadow: var(--glass-shadow);
  }

  /* Logo hover effects */
  .logo:hover .w-10 {
    transform: scale(1.05) rotate(5deg);
  }

  /* Mobile menu animations */
  #mobile-menu {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  /* Ensure mobile menu is hidden on desktop */
  @media (min-width: 768px) {
    #mobile-menu {
      display: none !important;
    }
  }

  /* Smooth mobile menu button animation */
  #mobile-menu-toggle:hover {
    transform: scale(1.05);
  }

  #mobile-menu-toggle:active {
    transform: scale(0.95);
  }
</style>