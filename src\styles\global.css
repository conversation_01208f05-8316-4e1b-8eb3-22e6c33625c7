@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&family=JetBrains+Mono:wght@400;500;600&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

/* Base styles */
* {
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
  font-feature-settings: 'cv02', 'cv03', 'cv04', 'cv11';
}

body {
  margin: 0;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  line-height: 1.6;
  transition: background-color 0.3s ease, color 0.3s ease;

  /* Optimize font rendering */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
  font-variant-ligatures: common-ligatures;
}

main {
  flex-grow: 1;
}

/* Improved focus styles for accessibility */
*:focus {
  outline: 2px solid theme('colors.primary.500');
  outline-offset: 2px;
}

*:focus:not(:focus-visible) {
  outline: none;
}

*:focus-visible {
  outline: 2px solid theme('colors.primary.500');
  outline-offset: 2px;
}

/* Performance optimizations */
img {
  height: auto;
  image-rendering: -webkit-optimize-contrast;
  image-rendering: crisp-edges;
}

img[loading="lazy"] {
  transition: opacity 0.3s ease-in-out;
}

/* Respect user motion preferences */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

/* Component Styles */
@layer components {
  /* Navigation styles */
  .nav-link {
    @apply relative px-3 py-2 text-sm font-medium transition-all duration-300;
    @apply text-gray-600 hover:text-blue-600;
    @apply dark:text-gray-300 dark:hover:text-blue-400;
  }

  .nav-link::after {
    content: '';
    @apply absolute bottom-0 left-0 w-full h-0.5;
    @apply bg-gradient-to-r from-blue-500 to-orange-500;
    @apply transform scale-x-0 transition-transform duration-300 origin-right;
  }

  .nav-link:hover::after,
  .nav-link.active::after {
    @apply scale-x-100 origin-left;
  }
}

/* Utility Classes */
@layer utilities {
  /* Line clamping utilities */
  .line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
}