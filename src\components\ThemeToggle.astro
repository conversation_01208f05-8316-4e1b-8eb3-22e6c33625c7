---
// Theme toggle component for dark/light mode switching
---

<button
  id="theme-toggle"
  class="theme-toggle relative p-2 rounded-xl bg-gray-100 hover:bg-gray-200 dark:bg-secondary-800 dark:hover:bg-secondary-700 transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 border border-gray-200 dark:border-gray-600"
  aria-label="Toggle dark mode"
  title="Toggle dark mode"
>
  <!-- Sun icon (visible in dark mode) -->
  <svg
    class="sun-icon w-5 h-5 text-gray-700 dark:text-secondary-300 transition-all duration-300 transform dark:rotate-180 dark:scale-0"
    fill="none"
    stroke="currentColor"
    viewBox="0 0 24 24"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      stroke-linecap="round"
      stroke-linejoin="round"
      stroke-width="2"
      d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z"
    ></path>
  </svg>
  
  <!-- Moon icon (visible in light mode) -->
  <svg
    class="moon-icon absolute top-2 left-2 w-5 h-5 text-gray-700 dark:text-secondary-300 transition-all duration-300 transform scale-0 dark:scale-100 dark:rotate-0"
    fill="none"
    stroke="currentColor"
    viewBox="0 0 24 24"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      stroke-linecap="round"
      stroke-linejoin="round"
      stroke-width="2"
      d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z"
    ></path>
  </svg>
</button>

<script>
  // Theme toggle functionality
  function initThemeToggle() {
    const themeToggle = document.getElementById('theme-toggle');
    const html = document.documentElement;
    
    // Check for saved theme preference or default to light mode
    const savedTheme = localStorage.getItem('theme');
    const systemPrefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
    
    // Set initial theme
    if (savedTheme === 'dark' || (!savedTheme && systemPrefersDark)) {
      html.classList.add('dark');
    } else {
      html.classList.remove('dark');
    }
    
    // Toggle theme function
    function toggleTheme() {
      const isDark = html.classList.contains('dark');
      
      if (isDark) {
        html.classList.remove('dark');
        localStorage.setItem('theme', 'light');
      } else {
        html.classList.add('dark');
        localStorage.setItem('theme', 'dark');
      }
      
      // Dispatch custom event for other components
      window.dispatchEvent(new CustomEvent('theme-changed', {
        detail: { theme: isDark ? 'light' : 'dark' }
      }));
    }
    
    // Add click event listener
    themeToggle?.addEventListener('click', toggleTheme);
    
    // Listen for system theme changes
    window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', (e) => {
      if (!localStorage.getItem('theme')) {
        if (e.matches) {
          html.classList.add('dark');
        } else {
          html.classList.remove('dark');
        }
      }
    });
  }
  
  // Initialize on DOM content loaded
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initThemeToggle);
  } else {
    initThemeToggle();
  }
  
  // Re-initialize on page navigation (for SPA-like behavior)
  document.addEventListener('astro:page-load', initThemeToggle);
</script>

<style>
  .theme-toggle {
    position: relative;
    overflow: hidden;
  }
  
  .theme-toggle:hover {
    transform: scale(1.05);
  }
  
  .theme-toggle:active {
    transform: scale(0.95);
  }
  
  /* Smooth icon transitions */
  .sun-icon,
  .moon-icon {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }
  
  /* Add subtle glow effect on hover */
  .theme-toggle:hover .sun-icon,
  .theme-toggle:hover .moon-icon {
    filter: drop-shadow(0 0 8px currentColor);
  }
</style>
