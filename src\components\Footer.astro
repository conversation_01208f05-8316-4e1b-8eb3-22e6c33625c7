---
const currentYear = new Date().getFullYear();
---

<footer role="contentinfo" class="bg-dark text-white py-12">
  <div class="container mx-auto px-5 max-w-6xl">
    <div class="grid grid-cols-1 md:grid-cols-2 gap-10 items-center">
      <div class="footer-info text-left">
        <p class="mb-0 leading-relaxed">&copy; {currentYear} Nob Hokleng. All rights reserved.</p>
        <p class="development-note mt-2 text-white/70 text-sm">This site is currently under development. Expected completion: July 2025</p>
      </div>
      <div class="footer-links">
        <nav aria-label="Footer navigation">
          <ul class="flex justify-center md:justify-end space-x-6 flex-wrap">
            <li><a href="/#about" class="footer-link text-white/80 font-medium transition-colors duration-300 hover:text-white relative py-1" aria-label="Go to About section">About</a></li>
            <li><a href="/#portfolio" class="footer-link text-white/80 font-medium transition-colors duration-300 hover:text-white relative py-1" aria-label="Go to Portfolio section">Portfolio</a></li>
            <li><a href="/contact" class="footer-link text-white/80 font-medium transition-colors duration-300 hover:text-white relative py-1" aria-label="Go to Contact section">Contact</a></li>
            <li><a href="/rss.xml" class="footer-link text-white/80 font-medium transition-colors duration-300 hover:text-white relative py-1" aria-label="Subscribe to RSS feed">RSS</a></li>
          </ul>
        </nav>
      </div>
    </div>
  </div>
</footer>

<style>
.footer-link::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 2px;
  background: linear-gradient(90deg, #3a86ff, #ff9e00);
  transform: scaleX(0);
  transform-origin: right;
  transition: transform 0.3s ease;
}

.footer-link:hover::after {
  transform: scaleX(1);
  transform-origin: left;
}
</style> 