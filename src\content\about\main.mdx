---
title: "About Nob Hokleng"
updatedDate: 2024-12-15
sections:
  - heading: "Professional Journey"
    content: "With over 8 years of experience in software development, I specialize in building scalable backend systems and leading high-performance engineering teams. My journey has taken me from startup environments to enterprise-scale platforms, where I've consistently delivered solutions that handle millions of users and transactions."
    subsections:
      - subheading: "Current Focus"
        content: "As a Senior Backend Engineer and System Architect, I focus on designing and implementing distributed systems that can scale from thousands to millions of users. My expertise lies in microservices architecture, performance optimization, and building robust, maintainable systems that support business growth."
      - subheading: "Leadership Experience"
        content: "I've led teams of 5-8 engineers through complex system migrations and greenfield projects, mentoring developers on best practices and modern architecture patterns. My leadership philosophy centers on empowering team members while maintaining high technical standards."
  - heading: "Technical Expertise"
    content: "My technical skills span the full spectrum of backend development, from low-level performance optimization to high-level system architecture design."
    subsections:
      - subheading: "Backend Development"
        content: "Expert in Java ecosystem with deep knowledge of Spring Boot, microservices patterns, and RESTful API design. I've built systems processing millions of requests daily with sub-second response times."
        items:
          - "Java & Spring Boot (Expert level - 6+ years)"
          - "Microservices Architecture & Domain-Driven Design"
          - "RESTful APIs, GraphQL, and WebSocket implementations"
          - "Performance optimization and JVM tuning"
          - "Unit testing, integration testing, and TDD practices"
      - subheading: "Database & Data Management"
        content: "Extensive experience with both SQL and NoSQL databases, including query optimization, sharding strategies, and data modeling for high-performance applications."
        items:
          - "MySQL, PostgreSQL - Advanced query optimization"
          - "MongoDB, Redis - Document stores and caching"
          - "Database design, indexing, and performance tuning"
          - "Data migration and ETL processes"
          - "ACID compliance and distributed transactions"
      - subheading: "DevOps & Infrastructure"
        content: "Strong background in cloud infrastructure, containerization, and CI/CD pipeline design, enabling rapid and reliable software delivery."
        items:
          - "Docker, Kubernetes - Container orchestration"
          - "AWS (EC2, RDS, Lambda, ELB, CloudWatch)"
          - "CI/CD pipelines with Jenkins, GitHub Actions"
          - "Infrastructure as Code with Terraform"
          - "Monitoring and alerting with Prometheus, Grafana"
      - subheading: "Architecture & Design"
        content: "Deep understanding of software architecture patterns and principles, with practical experience in designing systems for scale and maintainability."
        items:
          - "Microservices and Domain-Driven Design"
          - "Event-driven architecture and CQRS patterns"
          - "API Gateway and service mesh implementation"
          - "Caching strategies and performance optimization"
          - "Security best practices and compliance"
  - heading: "Professional Experience"
    content: "Throughout my career, I've worked on diverse projects ranging from e-commerce platforms to financial services, consistently delivering high-quality solutions."
    subsections:
      - subheading: "Senior Backend Engineer | TechCorp (2022-Present)"
        content: "Leading backend development for a financial services platform serving 50K+ daily active users."
        items:
          - "Architected and implemented microservices migration from legacy monolith"
          - "Optimized critical API performance, reducing response times by 85%"
          - "Led team of 6 engineers through complex system redesign"
          - "Implemented comprehensive monitoring and alerting systems"
          - "Reduced infrastructure costs by 30% through optimization"
      - subheading: "Software Engineer | ScaleStart (2020-2022)"
        content: "Full-stack development for a high-growth e-commerce platform, focusing on backend scalability and performance."
        items:
          - "Built real-time analytics system processing 1M+ events per minute"
          - "Designed and implemented event-driven architecture for order processing"
          - "Optimized database queries reducing load times by 60%"
          - "Implemented automated testing and deployment pipelines"
          - "Mentored junior developers on best practices and architecture"
      - subheading: "Backend Developer | StartupVenture (2018-2020)"
        content: "Early-stage startup experience building products from MVP to production scale."
        items:
          - "Developed REST APIs serving mobile and web applications"
          - "Implemented user authentication and authorization systems"
          - "Built data processing pipelines for analytics"
          - "Contributed to product architecture and technical decisions"
          - "Worked directly with product team on feature development"
  - heading: "Key Achievements"
    content: "Throughout my career, I've delivered measurable impact through technical excellence and strategic thinking."
    subsections:
      - subheading: "Performance Engineering"
        items:
          - "Reduced API response times by 85% (2.3s to 350ms) for financial services platform"
          - "Optimized database queries achieving 70% reduction in execution time"
          - "Implemented caching strategies with 92% hit rates"
          - "Designed systems handling 15K+ concurrent users with 99.99% uptime"
      - subheading: "System Architecture"
        items:
          - "Led successful migration of monolith to 15 microservices with zero downtime"
          - "Architected real-time analytics processing 1.2M events per minute"
          - "Designed event-driven systems supporting 300% business growth"
          - "Implemented distributed systems with 99.97% availability"
      - subheading: "Team Leadership"
        items:
          - "Mentored 12+ engineers on architecture and best practices"
          - "Led cross-functional teams delivering major platform migrations"
          - "Established development standards and code review processes"
          - "Improved team velocity by 300% through process optimization"
      - subheading: "Business Impact"
        items:
          - "Enabled 300% revenue growth through scalable platform architecture"
          - "Reduced infrastructure costs by $200K annually through optimization"
          - "Improved user satisfaction scores by 45% through performance improvements"
          - "Decreased time-to-market for new features by 60%"
  - heading: "Education & Continuous Learning"
    content: "I believe in continuous learning and staying current with industry trends and emerging technologies."
    subsections:
      - subheading: "Formal Education"
        items:
          - "Bachelor's Degree in Computer Science"
          - "Focus on Software Engineering and Database Systems"
      - subheading: "Certifications & Training"
        items:
          - "AWS Certified Solutions Architect"
          - "Spring Professional Certification"
          - "Kubernetes Application Developer (CKAD)"
          - "Advanced Java Performance Tuning"
      - subheading: "Continuous Learning"
        content: "I actively engage with the developer community and stay updated with latest technologies and best practices."
        items:
          - "Regular contributor to open-source projects"
          - "Technical blog writer on system architecture and performance"
          - "Conference speaker on microservices and performance optimization"
          - "Active participant in local developer meetups and communities"
  - heading: "Personal Approach"
    content: "My development philosophy centers on building systems that are not just functional, but maintainable, scalable, and performant."
    subsections:
      - subheading: "Core Principles"
        items:
          - "Code should be written for humans to read and maintain"
          - "Performance and scalability should be considered from day one"
          - "Testing is not optional - it's essential for reliable systems"
          - "Documentation and knowledge sharing enable team success"
          - "Simple solutions are often the best solutions"
      - subheading: "Problem-Solving Approach"
        content: "I approach technical challenges with a systematic methodology that prioritizes understanding before implementation."
        items:
          - "Start with thorough analysis and requirements gathering"
          - "Design for scalability and future growth from the beginning"
          - "Implement iteratively with continuous feedback and improvement"
          - "Monitor and measure everything to enable data-driven decisions"
          - "Document solutions and share knowledge with the team"
      - subheading: "Collaboration Style"
        content: "I believe great software is built by great teams, and I focus on enabling others to do their best work."
        items:
          - "Clear communication and transparent technical discussions"
          - "Mentoring and knowledge sharing with team members"
          - "Collaborative code reviews focused on learning and improvement"
          - "Cross-functional collaboration with product and design teams"
          - "Building consensus around technical decisions and architecture"
  - heading: "Personal Interests"
    content: "Outside of work, I'm passionate about technology, learning, and contributing to the developer community."
    subsections:
      - subheading: "Technology & Innovation"
        items:
          - "Following emerging trends in distributed systems and cloud computing"
          - "Experimenting with new programming languages and frameworks"
          - "Contributing to open-source projects and technical communities"
          - "Building side projects to explore new technologies"
      - subheading: "Knowledge Sharing"
        items:
          - "Writing technical articles on system design and performance"
          - "Speaking at conferences and local meetups"
          - "Mentoring junior developers and career changers"
          - "Participating in technical podcasts and discussions"
---

# About Nob Hokleng

A passionate backend engineer and system architect with a track record of building scalable, high-performance systems that power modern applications and support business growth.

I specialize in transforming complex technical challenges into elegant, maintainable solutions that can scale from startup to enterprise level. With deep expertise in microservices architecture, performance optimization, and team leadership, I help organizations build the technical foundation they need to succeed.

My approach combines technical excellence with practical business sense, ensuring that the systems I build not only perform well today but can evolve and scale as requirements change. I'm passionate about mentoring others, sharing knowledge, and contributing to the broader developer community.

When I'm not writing code or designing systems, you can find me exploring new technologies, contributing to open-source projects, or sharing insights about software architecture and engineering best practices.