#!/usr/bin/env node

/**
 * Performance Testing Script for NobiSite Portfolio
 * Tests Core Web Vitals and performance metrics
 */

const fs = require('fs');
const path = require('path');

// Performance testing configuration
const config = {
  // URLs to test (add your deployed URL here)
  urls: [
    'http://localhost:4321',
    'http://localhost:4321/about',
    'http://localhost:4321/portfolio',
    'http://localhost:4321/contact',
    'http://localhost:4321/resources',
    'http://localhost:4321/resume'
  ],
  
  // Performance targets from TaskMaster
  targets: {
    lighthouse: {
      performance: 98,
      accessibility: 95,
      bestPractices: 95,
      seo: 95
    },
    coreWebVitals: {
      lcp: 2500,  // Largest Contentful Paint (ms)
      fid: 100,   // First Input Delay (ms)
      cls: 0.1,   // Cumulative Layout Shift
      fcp: 1800,  // First Contentful Paint (ms)
      ttfb: 600   // Time to First Byte (ms)
    },
    loadTime: 800 // Page load time target (ms)
  }
};

/**
 * Simple performance test using basic metrics
 * Note: For full Lighthouse testing, use: npx lighthouse <url> --output=json
 */
async function runBasicPerformanceTest() {
  console.log('🚀 Starting Performance Test for NobiSite Portfolio\n');
  
  const results = {
    timestamp: new Date().toISOString(),
    tests: [],
    summary: {
      passed: 0,
      failed: 0,
      warnings: 0
    }
  };
  
  // Test each URL
  for (const url of config.urls) {
    console.log(`Testing: ${url}`);
    
    try {
      const testResult = await testURL(url);
      results.tests.push(testResult);
      
      if (testResult.status === 'pass') {
        results.summary.passed++;
        console.log(`✅ ${url} - PASSED`);
      } else if (testResult.status === 'warning') {
        results.summary.warnings++;
        console.log(`⚠️  ${url} - WARNING`);
      } else {
        results.summary.failed++;
        console.log(`❌ ${url} - FAILED`);
      }
      
    } catch (error) {
      console.error(`❌ Error testing ${url}:`, error.message);
      results.tests.push({
        url,
        status: 'error',
        error: error.message
      });
      results.summary.failed++;
    }
    
    console.log(''); // Empty line for readability
  }
  
  // Generate report
  generateReport(results);
  
  return results;
}

/**
 * Test a single URL for basic performance metrics
 */
async function testURL(url) {
  const startTime = Date.now();
  
  try {
    // Basic fetch test (simulates page load)
    const response = await fetch(url);
    const endTime = Date.now();
    const loadTime = endTime - startTime;
    
    const result = {
      url,
      loadTime,
      status: response.ok ? 'pass' : 'fail',
      statusCode: response.status,
      contentLength: response.headers.get('content-length') || 'unknown',
      contentType: response.headers.get('content-type') || 'unknown',
      timestamp: new Date().toISOString()
    };
    
    // Check against targets
    if (loadTime > config.targets.loadTime) {
      result.status = 'warning';
      result.warnings = [`Load time ${loadTime}ms exceeds target ${config.targets.loadTime}ms`];
    }
    
    return result;
    
  } catch (error) {
    return {
      url,
      status: 'error',
      error: error.message,
      timestamp: new Date().toISOString()
    };
  }
}

/**
 * Generate performance report
 */
function generateReport(results) {
  console.log('\n📊 Performance Test Results Summary');
  console.log('=====================================');
  console.log(`✅ Passed: ${results.summary.passed}`);
  console.log(`⚠️  Warnings: ${results.summary.warnings}`);
  console.log(`❌ Failed: ${results.summary.failed}`);
  console.log(`📅 Test Date: ${results.timestamp}\n`);
  
  // Detailed results
  console.log('📋 Detailed Results:');
  console.log('--------------------');
  
  results.tests.forEach((test, index) => {
    console.log(`${index + 1}. ${test.url}`);
    console.log(`   Status: ${test.status.toUpperCase()}`);
    
    if (test.loadTime) {
      console.log(`   Load Time: ${test.loadTime}ms`);
      console.log(`   Status Code: ${test.statusCode}`);
      console.log(`   Content Type: ${test.contentType}`);
    }
    
    if (test.warnings) {
      console.log(`   Warnings: ${test.warnings.join(', ')}`);
    }
    
    if (test.error) {
      console.log(`   Error: ${test.error}`);
    }
    
    console.log('');
  });
  
  // Save results to file
  const reportPath = path.join(__dirname, '..', 'performance-report.json');
  fs.writeFileSync(reportPath, JSON.stringify(results, null, 2));
  console.log(`📄 Full report saved to: ${reportPath}`);
  
  // Performance recommendations
  console.log('\n💡 Performance Optimization Recommendations:');
  console.log('--------------------------------------------');
  console.log('1. ✅ Image optimization implemented');
  console.log('2. ✅ Font loading optimized');
  console.log('3. ✅ Service Worker caching enabled');
  console.log('4. ✅ Critical CSS optimization added');
  console.log('5. ✅ Performance monitoring implemented');
  console.log('6. 🔄 Next: Run Lighthouse audit for detailed metrics');
  console.log('7. 🔄 Next: Test on real devices and networks');
  console.log('8. 🔄 Next: Implement image lazy loading in components');
}

/**
 * Lighthouse testing instructions
 */
function showLighthouseInstructions() {
  console.log('\n🔍 For Complete Lighthouse Testing:');
  console.log('===================================');
  console.log('1. Install Lighthouse CLI: npm install -g lighthouse');
  console.log('2. Start your dev server: npm run dev');
  console.log('3. Run Lighthouse tests:');
  console.log('   lighthouse http://localhost:4321 --output=json --output-path=./lighthouse-report.json');
  console.log('   lighthouse http://localhost:4321/portfolio --output=json --output-path=./lighthouse-portfolio.json');
  console.log('4. View results in lighthouse-report.json');
  console.log('\n📱 Mobile Testing:');
  console.log('   lighthouse http://localhost:4321 --preset=mobile --output=json --output-path=./lighthouse-mobile.json');
}

// Run the performance test
if (require.main === module) {
  runBasicPerformanceTest()
    .then((results) => {
      showLighthouseInstructions();
      
      const exitCode = results.summary.failed > 0 ? 1 : 0;
      process.exit(exitCode);
    })
    .catch((error) => {
      console.error('❌ Performance test failed:', error);
      process.exit(1);
    });
}

module.exports = {
  runBasicPerformanceTest,
  config
};
