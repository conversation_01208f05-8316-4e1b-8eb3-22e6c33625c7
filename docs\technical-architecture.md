# Technical Architecture

## Technology Stack Overview

### Core Technologies
- **Frontend Framework:** Astro.js 4.0+ with TypeScript
- **CSS Framework:** Tailwind CSS 3.4+ with custom design system
- **Content Management:** MDX with Frontmatter for dynamic content
- **Build Tools:** Vite (integrated with Astro)
- **Package Manager:** pnpm for efficient dependency management
- **Development:** TypeScript for type safety and better DX

### Deployment & Infrastructure
- **Hosting Platform:** Vercel (Primary) + Netlify (Backup)
- **Analytics:** Vercel Analytics + Google Analytics 4
- **Domain:** Custom domain with automatic HTTPS
- **CI/CD:** Automatic deployment from GitHub

## Architecture Decisions

### Frontend Framework: Astro.js

**Why Astro.js over alternatives:**
- **Performance-first**: Ships zero JavaScript by default, only hydrates interactive components
- **Perfect for portfolios**: Designed specifically for content-focused, static sites
- **SEO excellence**: Built-in SSG with automatic sitemap generation and meta tag management
- **Component flexibility**: Can integrate React, Vue, or vanilla JS components when needed
- **Developer experience**: Modern tooling with hot reload and TypeScript support
- **Lighthouse scores**: Consistently achieves 95+ performance scores
- **Learning curve**: Minimal for developers with HTML/CSS/JS experience

#### Framework Comparison Matrix

| Feature | Astro.js | Next.js | Nuxt.js | Static HTML |
|---------|----------|---------|---------|-------------|
| **Performance** | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐ | ⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ |
| **SEO** | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐ | ⭐⭐⭐⭐ | ⭐⭐⭐ |
| **Developer Experience** | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐ | ⭐⭐ |
| **Learning Curve** | ⭐⭐⭐⭐⭐ | ⭐⭐⭐ | ⭐⭐⭐ | ⭐⭐⭐⭐⭐ |
| **Bundle Size** | ⭐⭐⭐⭐⭐ | ⭐⭐⭐ | ⭐⭐⭐ | ⭐⭐⭐⭐⭐ |
| **Portfolio Fit** | ⭐⭐⭐⭐⭐ | ⭐⭐⭐ | ⭐⭐⭐ | ⭐⭐⭐⭐ |

### CSS Framework: Tailwind CSS

**Why Tailwind CSS:**
- **Developer productivity**: Utility-first approach speeds up development significantly
- **Consistency**: Built-in design system with consistent spacing, colors, and typography
- **Performance**: Automatically purges unused CSS for optimal bundle size
- **Maintainability**: Better than pure CSS for larger projects, easier to refactor
- **Responsive design**: Built-in responsive utilities for all breakpoints
- **Customization**: Easy to extend with custom utilities and components

### Hosting Platform: Vercel

**Primary Choice:** Vercel with GitHub integration
- **Performance:** Global edge network with automatic optimizations
- **Deployment:** Automatic deployments on Git push with preview URLs
- **Analytics:** Built-in Web Vitals monitoring and performance insights
- **Cost:** Free tier with generous limits for portfolio sites
- **Developer Experience:** Seamless integration with modern frameworks

**Backup Option:** Netlify
- **Reliability:** Alternative hosting with similar features
- **Form Handling:** Excellent built-in form processing capabilities
- **Edge Functions:** Serverless functions for dynamic functionality

### Content Management: MDX + Content Collections

**File-based content management benefits:**
- **Developer-Friendly:** Markdown with React component support
- **Version Control:** Content stored in Git repository
- **Type Safety:** TypeScript schemas for content validation
- **Flexibility:** Easy to add interactive elements to content
- **No External Dependencies:** Reduces complexity and potential failures

## Project Structure

```
nobi-site/
├── src/
│   ├── components/          # Reusable Astro components
│   │   ├── Header.astro     # Navigation component
│   │   ├── Footer.astro     # Footer component
│   │   └── ProjectCard.astro # Portfolio item display
│   ├── content/             # MDX content with schemas
│   │   ├── config.ts        # Zod validation schemas
│   │   ├── portfolio/       # Project case studies
│   │   ├── resources/       # Resource links
│   │   └── about/           # About page content
│   ├── layouts/             # Page templates
│   │   └── Layout.astro     # Main layout with SEO
│   ├── pages/               # File-based routing
│   │   ├── index.astro      # Homepage
│   │   ├── about.astro      # About page
│   │   ├── contact.astro    # Contact page
│   │   ├── resume.astro     # Resume page
│   │   ├── resources.astro  # Resources page
│   │   └── portfolio/       # Portfolio section
│   ├── styles/              # Global CSS + Tailwind
│   └── utils/               # Utility functions
├── docs/                    # Project documentation
├── dist/                    # Built output (generated)
└── public/                  # Static assets
```

## Performance Optimizations

### Static Generation Benefits
- **Pre-built HTML** - Pages load instantly from CDN
- **Asset optimization** - Automatic image compression and lazy loading
- **Bundle splitting** - Code splitting for optimal caching
- **CDN-ready** - Optimized for global content delivery

### SEO & Accessibility Features
- **Meta tag management** - Dynamic titles, descriptions, Open Graph
- **Structured data** - JSON-LD for search engines
- **Semantic HTML** - Proper heading hierarchy and ARIA labels
- **WCAG 2.1 AA compliance** - Accessibility best practices

## Development Workflow

### Development Commands
```bash
pnpm dev      # Start dev server with hot reload
pnpm build    # Production build
pnpm preview  # Preview built site locally
pnpm check    # TypeScript validation
```

### Modern Tooling
- **Vite bundler** - Fast builds with hot module replacement
- **TypeScript** - Full type safety and intellisense
- **Hot reload** - Instant updates during development
- **Component reusability** - Modular architecture

## Quality Standards

### Performance Targets
- **Lighthouse Score:** 95+ across all metrics
- **Load Time:** <1.2 seconds first contentful paint
- **Bundle Size:** Minimal JavaScript footprint
- **Core Web Vitals:** Excellent ratings for LCP, FID, CLS

### Code Quality
- **TypeScript:** Strict mode enabled for type safety
- **ESLint:** Code quality and consistency enforcement
- **Prettier:** Automated code formatting
- **Component Architecture:** Reusable, maintainable components

### Accessibility Standards
- **WCAG 2.1 AA compliance** - Full accessibility support
- **Semantic HTML** - Proper document structure
- **Keyboard navigation** - Full keyboard accessibility
- **Screen reader support** - ARIA labels and descriptions

## Why This Stack?

**Perfect for portfolios because:**
- **Lightning fast** - Static sites load instantly
- **SEO optimized** - Great for discoverability
- **Low maintenance** - No servers or databases to manage
- **Cost effective** - Static hosting is often free
- **Developer friendly** - Modern tooling and type safety
- **Future-proof** - Easy to add dynamic features later

This architecture demonstrates modern web development best practices with excellent performance, maintainability, and scalability for a professional portfolio website.
