---
import Layout from '../layouts/Layout.astro';
import ProjectCard from '../components/ProjectCard.astro';
import { getEntry, getCollection } from 'astro:content';

const homepageContent = await getEntry('homepage', 'main');
const { hero, about } = homepageContent.data;

// Get latest portfolio projects for preview
const portfolioProjects = await getCollection('portfolio');
const featuredProjects = portfolioProjects
  .sort((a, b) => b.data.publishDate.getTime() - a.data.publishDate.getTime())
  .slice(0, 3);
---

<Layout title="Nob Hokleng | Software Developer & System Architect">
  <!-- Hero Section -->
  <section class="hero relative min-h-screen flex items-center justify-center overflow-hidden" role="banner">
    <!-- Modern gradient background -->
    <div class="absolute inset-0 bg-gradient-to-br from-white via-gray-50 to-primary-50/30 dark:from-gray-900 dark:via-gray-800 dark:to-primary-950/30"></div>

    <!-- Animated background elements with improved design -->
    <div class="absolute inset-0 overflow-hidden pointer-events-none">
      <!-- Floating geometric shapes -->
      <div class="absolute top-1/4 left-1/4 w-72 h-72 bg-gradient-to-br from-primary-400/10 to-accent-400/10 rounded-full blur-3xl animate-float"></div>
      <div class="absolute bottom-1/3 right-1/4 w-96 h-96 bg-gradient-to-br from-accent-400/10 to-primary-400/10 rounded-full blur-3xl animate-float" style="animation-delay: 2s; animation-duration: 4s;"></div>
      <div class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-64 h-64 bg-gradient-to-br from-primary-300/5 to-accent-300/5 rounded-full blur-2xl animate-bounce-subtle"></div>

      <!-- Grid pattern overlay -->
      <div class="absolute inset-0 opacity-40" style="background-image: url('data:image/svg+xml,%3Csvg width=&quot;60&quot; height=&quot;60&quot; viewBox=&quot;0 0 60 60&quot; xmlns=&quot;http://www.w3.org/2000/svg&quot;%3E%3Cg fill=&quot;none&quot; fill-rule=&quot;evenodd&quot;%3E%3Cg fill=&quot;%239C92AC&quot; fill-opacity=&quot;0.02&quot;%3E%3Ccircle cx=&quot;30&quot; cy=&quot;30&quot; r=&quot;1&quot;/%3E%3C/g%3E%3C/g%3E%3C/svg%3E');"></div>
    </div>

    <div class="container mx-auto px-4 sm:px-6 lg:px-8 max-w-7xl relative z-10 py-20">
      <div class="text-center">

        <!-- Main heading with modern typography -->
        <div class="mb-8 animate-fade-in">
          <h1 class="text-4xl sm:text-5xl lg:text-6xl font-bold mb-6 leading-tight">
            <span class="bg-gradient-to-r from-blue-600 via-blue-500 to-orange-500 bg-clip-text text-transparent" set:html={hero.headline}></span>
          </h1>

          <h2 class="text-xl sm:text-2xl lg:text-3xl text-gray-600 dark:text-gray-300 mb-6 font-medium max-w-4xl mx-auto leading-relaxed">
            {hero.subheadline}
          </h2>

          <p class="text-lg sm:text-xl max-w-3xl mx-auto text-gray-600 dark:text-gray-400 leading-relaxed">
            {hero.description}
          </p>
        </div>

        <!-- Modern feature highlights with bento-style layout -->
        <div class="features grid grid-cols-2 lg:grid-cols-4 gap-4 mb-12 max-w-5xl mx-auto" style="animation-delay: 0.2s;">
          {hero.highlights.map((highlight, index) => (
            <div class="feature-item group bg-white dark:bg-gray-800 rounded-2xl p-6 text-center shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1 border border-gray-200 dark:border-gray-700" style={`animation-delay: ${0.3 + index * 0.1}s;`}>
              <div class="text-3xl mb-3 group-hover:scale-110 transition-transform duration-300">{highlight.icon}</div>
              <p class="text-sm font-medium text-gray-700 dark:text-gray-300 group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors duration-300">{highlight.label}</p>
            </div>
          ))}
        </div>

        <!-- Enhanced CTA buttons with modern design -->
        <div class="cta-buttons flex justify-center gap-4 flex-col sm:flex-row" style="animation-delay: 0.6s;">
          <a href={hero.primaryCTA.url} class="inline-flex items-center justify-center px-8 py-4 text-lg font-medium rounded-xl bg-gradient-to-r from-blue-500 to-blue-600 text-white shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1 group" aria-label="View my portfolio projects">
            {hero.primaryCTA.text}
            <svg class="w-5 h-5 ml-2 group-hover:translate-x-1 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"></path>
            </svg>
          </a>
          <a href={hero.secondaryCTA.url} class="inline-flex items-center justify-center px-8 py-4 text-lg font-medium rounded-xl bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-200 border border-gray-300 dark:border-gray-600 shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1 group" aria-label="Get in touch with me">
            {hero.secondaryCTA.text}
            <svg class="w-5 h-5 ml-2 group-hover:scale-110 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
            </svg>
          </a>
        </div>
        
        <!-- Scroll indicator -->
        <div class="scroll-indicator absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce">
          <svg class="w-6 h-6 text-primary/60" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 14l-7 7m0 0l-7-7m7 7V3"></path>
          </svg>
        </div>
      </div>
    </div>
  </section>

  <!-- About Section -->
  <section id="about" class="py-24 bg-white dark:bg-gray-900 relative overflow-hidden" role="main">
    <!-- Modern background elements -->
    <div class="absolute inset-0 pointer-events-none">
      <div class="absolute top-10 right-10 w-64 h-64 bg-gradient-to-br from-orange-400/5 to-transparent rounded-full blur-3xl"></div>
      <div class="absolute bottom-10 left-10 w-64 h-64 bg-gradient-to-tl from-blue-400/5 to-transparent rounded-full blur-3xl"></div>
    </div>

    <div class="container mx-auto px-4 sm:px-6 lg:px-8 max-w-7xl relative z-10">
      <div class="text-center mb-16">
        <h2 class="text-3xl sm:text-4xl lg:text-5xl font-bold text-gray-800 dark:text-gray-200 mb-4 relative">
          About Me
          <span class="absolute bottom-0 left-1/2 transform -translate-x-1/2 -mb-2 w-20 h-1 bg-gradient-to-r from-blue-500 to-orange-500 rounded"></span>
        </h2>
      </div>
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-16 items-start">
        <div class="about-text space-y-6">
          <p class="text-lg font-medium text-gray-700 dark:text-gray-300 leading-relaxed">
            {about.openingLine}
          </p>
          {about.mainContent.map((paragraph: string) => (
            <p class="text-base leading-relaxed text-gray-600 dark:text-gray-400">
              {paragraph}
            </p>
          ))}
        </div>
          
          <div class="experience-highlights mt-8">
            <h3 class="text-xl font-bold text-secondary mb-5 font-heading">Experience Highlights</h3>
            <ul class="space-y-2">
              <li class="flex items-center">
                <span class="text-primary mr-3 font-bold">✓</span>
                <span class="text-text">Designed and implemented scalable backend systems</span>
              </li>
              <li class="flex items-center">
                <span class="text-primary mr-3 font-bold">✓</span>
                <span class="text-text">Built high-performance APIs serving thousands of requests</span>
              </li>
              <li class="flex items-center">
                <span class="text-primary mr-3 font-bold">✓</span>
                <span class="text-text">Implemented DevOps practices and CI/CD pipelines</span>
              </li>
              <li class="flex items-center">
                <span class="text-primary mr-3 font-bold">✓</span>
                <span class="text-text">Worked with cloud platforms and containerization</span>
              </li>
              <li class="flex items-center">
                <span class="text-primary mr-3 font-bold">✓</span>
                <span class="text-text">Mentored team members and led technical initiatives</span>
              </li>
            </ul>
          </div>
        </div>
        
        <!-- Technical Skills -->
        <div class="skills-section">
          <div class="bg-gradient-to-br from-white via-white to-gray-50/50 p-8 lg:p-10 rounded-3xl shadow-xl border border-white/50 backdrop-blur-sm">
            <h3 class="text-2xl font-bold text-secondary mb-8 text-center font-heading flex items-center justify-center gap-3">
              <span class="w-8 h-8 bg-gradient-to-br from-primary to-accent rounded-lg flex items-center justify-center text-white text-sm">⚡</span>
              Technical Skills
            </h3>
            
            <div class="grid grid-cols-1 gap-6">
              <!-- Backend Development -->
              <div class="skill-category group bg-white/80 p-6 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1 border border-primary/10">
                <h4 class="text-lg font-semibold text-primary mb-4 font-heading flex items-center gap-3">
                  <span class="w-6 h-6 bg-gradient-to-br from-primary to-secondary rounded-lg flex items-center justify-center text-white text-xs">🔧</span>
                  Backend Development
                </h4>
                <div class="flex flex-wrap gap-2">
                  {["Java & Spring Boot", "Node.js & Express", "Python & FastAPI", "RESTful APIs", "GraphQL", "Microservices"].map(skill => (
                    <span class="skill-tag px-3 py-1.5 bg-gradient-to-r from-primary/10 to-secondary/10 text-primary text-sm font-medium rounded-full border border-primary/20 hover:bg-gradient-to-r hover:from-primary/20 hover:to-secondary/20 transition-all duration-300 hover:scale-105">
                      {skill}
                    </span>
                  ))}
                </div>
              </div>
              
              <!-- System Architecture -->
              <div class="skill-category group bg-white/80 p-6 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1 border border-secondary/10">
                <h4 class="text-lg font-semibold text-secondary mb-4 font-heading flex items-center gap-3">
                  <span class="w-6 h-6 bg-gradient-to-br from-secondary to-primary rounded-lg flex items-center justify-center text-white text-xs">🏗️</span>
                  System Architecture
                </h4>
                <div class="flex flex-wrap gap-2">
                  {["Event-Driven Systems", "Database Design", "Caching Strategies", "Load Balancing", "Message Queues", "API Gateway"].map(skill => (
                    <span class="skill-tag px-3 py-1.5 bg-gradient-to-r from-secondary/10 to-primary/10 text-secondary text-sm font-medium rounded-full border border-secondary/20 hover:bg-gradient-to-r hover:from-secondary/20 hover:to-primary/20 transition-all duration-300 hover:scale-105">
                      {skill}
                    </span>
                  ))}
                </div>
              </div>
              
              <!-- DevOps & Cloud -->
              <div class="skill-category group bg-white/80 p-6 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1 border border-accent/10">
                <h4 class="text-lg font-semibold text-accent mb-4 font-heading flex items-center gap-3">
                  <span class="w-6 h-6 bg-gradient-to-br from-accent to-orange-500 rounded-lg flex items-center justify-center text-white text-xs">☁️</span>
                  DevOps & Cloud
                </h4>
                <div class="flex flex-wrap gap-2">
                  {["Docker & Kubernetes", "AWS & Azure", "CI/CD Pipelines", "Terraform", "Monitoring & Logging", "Infrastructure as Code"].map(skill => (
                    <span class="skill-tag px-3 py-1.5 bg-gradient-to-r from-accent/10 to-orange-500/10 text-accent text-sm font-medium rounded-full border border-accent/20 hover:bg-gradient-to-r hover:from-accent/20 hover:to-orange-500/20 transition-all duration-300 hover:scale-105">
                      {skill}
                    </span>
                  ))}
                </div>
              </div>
            </div>
            
            <!-- Call to action -->
            <div class="mt-8 text-center">
              <a href="/resume" class="inline-flex items-center gap-2 px-6 py-3 bg-gradient-to-r from-primary to-secondary text-white rounded-xl font-semibold hover:shadow-lg transition-all duration-300 hover:-translate-y-1 text-sm">
                View Full Resume
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                </svg>
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- Portfolio Section -->
  <section id="portfolio" class="py-24 bg-gray-50 dark:bg-gray-800 relative overflow-hidden" role="region" aria-labelledby="portfolio-title">
    <!-- Modern background elements -->
    <div class="absolute inset-0 pointer-events-none">
      <div class="absolute top-20 left-10 w-72 h-72 bg-gradient-to-br from-blue-400/5 to-transparent rounded-full blur-3xl"></div>
      <div class="absolute bottom-20 right-10 w-72 h-72 bg-gradient-to-tl from-orange-400/5 to-transparent rounded-full blur-3xl"></div>
    </div>

    <div class="container mx-auto px-4 sm:px-6 lg:px-8 max-w-7xl relative z-10">
      <div class="text-center mb-16">
        <h2 id="portfolio-title" class="text-3xl sm:text-4xl lg:text-5xl font-bold text-gray-800 dark:text-gray-200 mb-4 relative">
          Featured Projects
          <span class="absolute bottom-0 left-1/2 transform -translate-x-1/2 -mb-2 w-20 h-1 bg-gradient-to-r from-blue-500 to-orange-500 rounded"></span>
        </h2>
        <p class="text-lg text-gray-600 dark:text-gray-400 max-w-2xl mx-auto">
          A showcase of scalable systems and innovative solutions I've built
        </p>
      </div>
      
      <!-- Modern Portfolio Grid -->
      <div class="portfolio-grid grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-12" role="list">
        {featuredProjects.length > 0 ? (
          featuredProjects.map((project, index) => (
            <div
              class="portfolio-item animate-on-scroll"
              style={`animation-delay: ${index * 0.1}s;`}
              role="listitem"
            >
              <a href={`/portfolio/${project.slug}`} class="block h-full">
                <ProjectCard
                  title={project.data.title}
                  description={project.data.problem + " " + project.data.solution}
                  tags={project.data.technologies}
                  slug={project.slug}
                />
              </a>
            </div>
          ))
        ) : (
          <!-- Fallback projects with modern design -->
          <>
            <div class="portfolio-item animate-on-scroll" role="listitem">
              <ProjectCard
                title="E-Commerce Platform Backend"
                description="Scalable microservices architecture for high-traffic e-commerce platform. Built with modern technologies to handle thousands of concurrent users and process millions of transactions."
                tags={["Java", "Spring Boot", "Microservices", "PostgreSQL", "Redis", "Docker"]}
              />
            </div>
            <div class="portfolio-item animate-on-scroll" style="animation-delay: 0.1s;" role="listitem">
              <ProjectCard
                title="Real-time Analytics System"
                description="Event-driven system processing millions of events daily. Provides real-time insights and analytics with low-latency data processing and visualization."
                tags={["Node.js", "Apache Kafka", "Redis", "MongoDB", "React", "WebSocket"]}
              />
            </div>
            <div class="portfolio-item animate-on-scroll" style="animation-delay: 0.2s;" role="listitem">
              <ProjectCard
                title="Cloud Infrastructure Platform"
                description="Automated CI/CD pipeline and infrastructure as code implementation. Streamlined deployment processes with monitoring, logging, and automated scaling."
                tags={["Docker", "Kubernetes", "AWS", "Terraform", "Jenkins", "Prometheus"]}
              />
            </div>
          </>
        )}
      </div>

      <!-- Modern Portfolio CTA -->
      <div class="text-center">
        <a href="/portfolio" class="inline-flex items-center justify-center px-8 py-4 text-lg font-medium rounded-xl bg-gradient-to-r from-blue-500 to-blue-600 text-white shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1 group">
          View All Projects
          <svg class="w-5 h-5 ml-2 group-hover:translate-x-1 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"></path>
          </svg>
        </a>
      </div>
    </div>
  </section>
  
  <!-- Call to Action Section -->
  <section class="cta-section py-24 bg-gradient-to-r from-secondary to-primary relative overflow-hidden">
    <div class="absolute inset-0 bg-black/10"></div>
    <div class="container mx-auto px-5 max-w-4xl relative z-10 text-center">
      <h2 class="text-3xl lg:text-4xl font-bold text-white mb-6 font-heading">
        Ready to Build Something Amazing?
      </h2>
      <p class="text-xl text-white/90 mb-8 max-w-2xl mx-auto leading-relaxed">
        Let's discuss your next project and how I can help you build scalable, high-performance solutions.
      </p>
      <div class="flex justify-center gap-6 flex-col sm:flex-row">
        <a href="/contact" class="group inline-flex items-center justify-center gap-3 px-8 py-4 bg-white text-primary rounded-2xl font-semibold shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1 text-lg">
          Start a Conversation
          <svg class="w-5 h-5 group-hover:scale-110 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
          </svg>
        </a>
        <a href="/resume" class="group inline-flex items-center justify-center gap-3 px-8 py-4 border-2 border-white text-white rounded-2xl font-semibold hover:bg-white hover:text-primary transition-all duration-300 hover:-translate-y-1 text-lg backdrop-blur-sm">
          Download Resume
          <svg class="w-5 h-5 group-hover:translate-y-1 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
          </svg>
        </a>
      </div>
    </div>
  </section>
</Layout> 